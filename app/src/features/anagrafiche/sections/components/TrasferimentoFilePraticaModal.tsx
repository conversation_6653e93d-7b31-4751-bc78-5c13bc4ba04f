import React, { useEffect } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    Typography,
} from "@vapor/v3-components";
import { Close } from "@mui/icons-material";
import { useTranslation } from "@1f/react-sdk";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import Spinner from "../../../../custom-components/Spinner";
import ToMergeListFilters from "../../../archive/components/ToMergeListFilters";
import { useToMergeListData } from "../../../archive/hooks/useToMergeListData";
import BottomToastNotification from "../../../../custom-components/Notification";

interface TrasferimentoFilePraticaDrawerProps {
    open: boolean;
    onClose: () => void;
    fileData: {
        id: string;
        nomefile: string;
        titolodocumento?: string;
        datadoc?: string;
    } | null;
}

const TrasferimentoFilePraticaDrawer: React.FC<TrasferimentoFilePraticaDrawerProps> = ({
    open,
    onClose,
    fileData,
}) => {
    const { t } = useTranslation();

    // Use the to-merge-list data hook
    const {
        query,
        setQuery,
        list,
        loading,
        resetFilters,
        refetch,
    } = useToMergeListData();

    // Reset filters when drawer opens to ensure fresh data
    useEffect(() => {
        if (open) {
            resetFilters();
        }
    }, [open, resetFilters]);

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleRowClick = (params: any) => {
        // Add transfer functionality here
        console.log("Row clicked for transfer:", params);
        // TODO: Implement file transfer to selected practice
    };

    return (
        <Drawer
            anchor="right"
            open={open}
            onClose={onClose}
            hideBackdrop
            sx={{
                "& .MuiDrawer-paper": {
                    width: "40%",
                    minWidth: "400px",
                    boxShadow:
                        "0px 0px 32px 8px rgba(0,0,0,0.22), 0px 4px 16px 0px rgba(0,0,0,0.18) !important",
                },
            }}
        >
            {/* Header */}
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    px: 3,
                    py: 2,
                }}
            >
                <Typography variant="h6" component="h2">
                    {t("TRASFERIMENTO FILE NELLA PRATICA")}
                </Typography>
                <IconButton
                    onClick={onClose}
                    color="primary"
                >
                    <Close />
                </IconButton>
            </Box>
            <Divider />

            {/* Content */}
            <Box
                sx={{
                    backgroundColor: "white",
                    flexGrow: 1,
                    p: 2,
                    display: "flex",
                    flexDirection: "column",
                    gap: 2,
                }}
            >
                {/* Notification Section */}
                <BottomToastNotification
                    showNotification={true}
                    text={t("Il file visualizzato sarà copiato nella cartella principale dei documenti della pratica. Avviare una ricerca o mostrare tutte le pratiche per visualizzare la lista.")}
                    notificationVariant="inline"
                    severity="info"
                    variant="outlined"
                />

                {/* Filters Section */}
                <ToMergeListFilters
                    query={query}
                    setQuery={setQuery}
                    onReset={resetFilters}
                />

                {/* Data Grid Section */}
                <Box
                    sx={{
                        flexGrow: 1,
                        display: "flex",
                        flexDirection: "column",
                        overflow: "hidden",
                    }}
                >
                    {loading ? (
                        <Spinner fullPage={false} />
                    ) : (
                        <>
                            {list.rows && list.rows.length > 0 ? (
                                <CustomDataGrid
                                    name="toMergeListTransfer"
                                    columns={list.columns}
                                    data={list.rows}
                                    page={list.page || 0}
                                    totalRows={list.totalRows}
                                    pageSize={list.pageSize}
                                    loading={loading}
                                    query={query}
                                    onPageChangeCallback={onPageChangeCallback}
                                    onClickCallback={handleRowClick}
                                    onClickKey="uniqueid"
                                    hasAdditionaStyles={false}
                                />
                            ) : (
                                <Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        minHeight: 200,
                                        gap: 2,
                                    }}
                                >
                                    <Typography variant="body2" color="text.secondary">
                                        {t("Elementi 1 - 10 Di 120")}
                                    </Typography>
                                </Box>
                            )}
                        </>
                    )}
                </Box>
            </Box>


        </Drawer>
    );
};

export default TrasferimentoFilePraticaDrawer;
