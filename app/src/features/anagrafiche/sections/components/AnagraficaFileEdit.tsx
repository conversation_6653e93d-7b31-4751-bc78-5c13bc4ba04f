import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, TextField, Typography, Dialog, DialogTitle, DialogContent, DialogActions, Button, IconButton, Divider } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFileUser } from "@fortawesome/pro-regular-svg-icons";
import { Close } from "@mui/icons-material";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import useGetCustom from "../../../../hooks/useGetCustom";

interface AnagraficaFileEditProps {
    open: boolean;
    onClose: () => void;
    onSave: (data: { description: string; date: string }) => void;
    fileData: {
        id: string;
        nomefile: string;
        titolodocumento: string;
        datadoc: string;
        filesize?: string;
        filetype?: string;
    } | null;
    loading?: boolean;
}

const AnagraficaFileEdit: React.FC<AnagraficaFileEditProps> = ({
    open,
    onClose,
    onSave,
    fileData,
    loading = false
}) => {
    const { t } = useTranslation();

    const [description, setDescription] = useState("");
    const [date, setDate] = useState<Date>(new Date());
    const [previewFile, setPreviewFile] = useState<any>(null);
    const [previewLoading, setPreviewLoading] = useState(false);

    const getPreviewUrl = useGetCustom(`anagrafiche/get-file-content`, {});

    const loadPreview = useCallback(async () => {
        if (!fileData?.id) return;

        const extension = fileData.nomefile.split(".").pop()?.toLowerCase();
        const supportedImageTypes = ["jpg", "jpeg", "png"];

        if (!supportedImageTypes.includes(extension || "")) {
            setPreviewFile(null);
            setPreviewLoading(false);
            return;
        }

        try {
            setPreviewLoading(true);
            const { data }: any = await getPreviewUrl.doFetch(true, { id: fileData.id });
            if (data?.base64) {
                setPreviewFile(data.base64);
            } else {
                console.error("Preview data not available or invalid format");
                setPreviewFile(null);
            }
        } catch (error) {
            console.error("Error loading preview:", error);
            setPreviewFile(null);
        } finally {
            setPreviewLoading(false);
        }
    }, [fileData?.id, fileData?.nomefile]);

    useEffect(() => {
        if (fileData) {
            setDescription(fileData.titolodocumento || "");
            if (fileData.datadoc) {
                const dateParts = fileData.datadoc.split("/");
                if (dateParts.length === 3) {
                    const [day, month, year] = dateParts;
                    setDate(new Date(parseInt(year), parseInt(month) - 1, parseInt(day)));
                } else {
                    setDate(new Date());
                }
            } else {
                setDate(new Date());
            }
        } else {
            setDescription("");
            setDate(new Date());
            setPreviewFile(null);
            setPreviewLoading(false);
        }
    }, [fileData]);

    useEffect(() => {
        if (open && fileData?.id) {
            setPreviewFile(null);
            setPreviewLoading(false);
            loadPreview();
        }
    }, [open, fileData?.id, loadPreview]);

    const formatDate = (date: Date): string => {
        const day = date.getDate().toString().padStart(2, "0");
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const year = date.getFullYear().toString();
        return `${day}/${month}/${year}`;
    };

    const handleSave = () => {
        onSave({
            description,
            date: formatDate(date)
        });
    };

    const handleCancel = () => {
        onClose();
    };

    const renderPreviewContent = () => {
        if (!fileData) return null;

        const extension = fileData.nomefile.split(".").pop()?.toLowerCase();
        const supportedImageTypes = ["jpg", "jpeg", "png"];

        if (!supportedImageTypes.includes(extension || "")) {
            return (
                <Box sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    flex: 1,
                    color: "text.secondary",
                    textAlign: "center"
                }}>
                    <Typography variant="body2">
                        {t("Anteprima disponibile solo per file JPEG e PNG")}
                    </Typography>
                </Box>
            );
        }

        if (previewFile) {
            const imageSrc = previewFile.startsWith('data:') ? previewFile : `data:image/jpeg;base64,${previewFile}`;
            return (
                <img
                    src={imageSrc}
                    alt={fileData.nomefile}
                    style={{
                        maxWidth: "100%",
                        maxHeight: "100%",
                        objectFit: "contain"
                    }}
                />
            );
        }

        return null;
    };


    const content = !fileData ? (
        <Box sx={{ p: 2 }}>
            <Typography>{t("Caricamento...")}</Typography>
        </Box>
    ) : (
        <Box sx={{ width: "fit-content", minWidth: 1000, maxWidth: 1200, p: 2 }}>
            <Box sx={{ mb: 2, pb: 1 }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                    <FontAwesomeIcon icon={faFileUser} fontSize={20} />
                    <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
                        {fileData.nomefile}
                    </Typography>
                </Box>
            </Box>

            <Box sx={{ display: "flex", gap: 2 }}>
                <Box sx={{
                    width: 500,
                    border: "1px solid #e0e0e0",
                    borderRadius: 1,
                    p: 1.5,
                    display: "flex",
                    flexDirection: "column"
                }}>
                    {previewLoading ? (
                        <Box sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            flex: 1,
                            color: "text.secondary"
                        }}>
                            {t("Caricamento anteprima...")}
                        </Box>
                    ) : previewFile ? (
                        <Box sx={{
                            flex: 1,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            overflow: "hidden"
                        }}>
                            {renderPreviewContent()}
                        </Box>
                    ) : (
                        <Box sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            flex: 1,
                            color: "text.secondary"
                        }}>
                            {t("Anteprima non disponibile")}
                        </Box>
                    )}
                </Box>

                <Box sx={{
                    width: 400,
                    p: 1.5,
                    display: "flex",
                    flexDirection: "column"
                }}>
                    <Box sx={{ display: "flex", flexDirection: "column", gap: 3, flex: 1 }}>
                        <Box>
                            <Typography variant="body2" sx={{ mb: 1, fontWeight: "medium" }}>
                                {t("Descrizione")}
                            </Typography>
                            <TextField
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                fullWidth
                                multiline
                                rows={6}
                                placeholder={t("Inserisci una descrizione per il file...")}
                            />
                        </Box>

                        <Box>
                            <Typography variant="body2" sx={{ mb: 1, fontWeight: "medium" }}>
                                {t("Data di aggiunta")}
                            </Typography>
                            <DatePicker
                                label={t("Seleziona data")}
                                name="editDate"
                                value={date}
                                onChange={(newDate: Date | null) => {
                                    if (newDate) setDate(newDate);
                                }}
                            />
                        </Box>
                    </Box>
                </Box>
            </Box>
        </Box>
    );

    return (
        <Dialog
            open={open}
            onClose={handleCancel}
            maxWidth={false}
            fullWidth={false}
            slotProps={{
                paper: {
                    sx: {
                        width: 'auto',
                        maxWidth: 'none',
                        margin: 2
                    }
                }
            }}
        >
            <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                {t("MODIFICA FILE")}
                <IconButton onClick={handleCancel} size="small">
                    <Close />
                </IconButton>
            </DialogTitle>
            <Divider />
            <DialogContent sx={{ p: 0 }}>
                {content}
            </DialogContent>
            <DialogActions sx={{ p: 2, gap: 1 }}>
                <Button variant="outlined" onClick={handleCancel}>
                    {t("Annulla")}
                </Button>
                <Button
                    variant="contained"
                    onClick={handleSave}
                    disabled={loading || !fileData}
                >
                    {loading ? t("Salvando...") : t("Salva")}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default AnagraficaFileEdit;
