import { useEffect } from "react";
import usePostCustom from "../../../hooks/usePostCustom";

export const useDeleteDocument = ({
    id,
    remove,
}: {
    id: string | undefined;
    remove: boolean;
}) => {
    const { doFetch, hasLoaded, loading, error } = usePostCustom(
        "anagrafiche/delete-file"
    );

    useEffect(() => {
        if (id && remove) {
            doFetch(true, { id: id }).catch((error) => {
                console.error("Failed to delete document:", error);
            });
        }
    }, [id, remove, doFetch]);

    return { loading, hasLoaded, error };
};
