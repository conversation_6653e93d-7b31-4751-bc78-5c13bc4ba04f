import moment from "moment";

export const updateParams = (params: any, date: any, isDownload = false) => {
  const { startagendaSearchValue, endagendaSearchValue, ...restParams } =
    params;


  if (date.startDateSearch) {
    restParams.startDateSearch = moment(date.startDateSearch, "DD/MM/YYYY").format(
      "DD/MM/YYYY"
    );
  } else {
    restParams.startDateSearch = "";
  }


  if (date.endDateSearch && params.endDateSearch) {
    restParams.endDateSearch = moment(date.endDateSearch, "DD/MM/YYYY").format("DD/MM/YYYY");
  } else {
    restParams.endDateSearch = "";
  }
  if (params.poliswebFilter) {
    restParams.poliswebFilter = "on";
  } else {
    delete restParams.poliswebFilter;
  }

  if (isDownload) {
    delete restParams.category;
    delete restParams.page;
    delete restParams.pageSize;
    delete restParams.sortOrder;
    delete restParams.sortColumn;
  }

  return restParams;
};
