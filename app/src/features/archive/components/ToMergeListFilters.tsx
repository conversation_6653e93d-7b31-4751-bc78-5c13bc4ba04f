import React from "react";
import { useTranslation } from "@1f/react-sdk";
import {
    Box,
    TextField,
    Button,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Stack,
    Typography,
} from "@vapor/react-material";
import { IToMergeListQuery } from "../hooks/useToMergeListData";
import debounce from "lodash/debounce";

interface ToMergeListFiltersProps {
    query: IToMergeListQuery;
    setQuery: (updates: Partial<IToMergeListQuery>) => void;
    onReset: () => void;
}

const ToMergeListFilters: React.FC<ToMergeListFiltersProps> = ({
    query,
    setQuery,
    onReset,
}) => {
    const { t } = useTranslation();

    // Debounced function for text inputs to avoid too many API calls
    const debouncedSetQuery = React.useMemo(
        () => debounce((updates: Partial<IToMergeListQuery>) => {
            setQuery(updates);
        }, 500),
        [setQuery]
    );

    const handleInputChange = (field: keyof IToMergeListQuery) => (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        const value = event.target.value;
        debouncedSetQuery({ [field]: value });
    };

    const handleSelectChange = (field: keyof IToMergeListQuery) => (
        event: any
    ) => {
        const value = event.target.value;
        setQuery({ [field]: value });
    };



    const codeTypeOptions = [
        { value: -1, label: t("Tutti i codici") },
        { value: 0, label: t("Codice Archivio") },
        { value: 1, label: t("Protocollo Generale") },
        { value: 2, label: t("RGN") },
        { value: 3, label: t("RGA") },
    ];

    return (
        <Box
            sx={{
                backgroundColor: "white",
                borderRadius: 1,
                p: 2,
                border: "1px solid #e0e0e0",
            }}
        >
            <Stack spacing={2}>
                {/* Single Row with all filters */}
                <Stack direction="row" spacing={2} alignItems="center" flexWrap="wrap">
                    <FormControl sx={{ minWidth: 150 }}>
                        <Select
                            value={query.codeType}
                            onChange={handleSelectChange("codeType")}
                            displayEmpty
                            size="small"
                        >
                            {codeTypeOptions.map((option) => (
                                <MenuItem key={option.value} value={option.value}>
                                    {option.label}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    <TextField
                        label={t("Cliente")}
                        value={query.customer}
                        onChange={handleInputChange("customer")}
                        size="small"
                        sx={{ minWidth: 150 }}
                    />

                    <TextField
                        label={t("Controparte")}
                        value={query.counterpart}
                        onChange={handleInputChange("counterpart")}
                        size="small"
                        sx={{ minWidth: 150 }}
                    />

                    <Button
                        variant="text"
                        onClick={onReset}
                        size="small"
                    >
                        {t("Mostra tutte")}
                    </Button>
                </Stack>
            </Stack>
        </Box>
    );
};

export default ToMergeListFilters;
