import React, { useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { Button, Box, Typography } from "@vapor/react-material";
import ToMergeListDrawer from "./ToMergeListDrawer";

/**
 * Example component demonstrating how to use the ToMergeListDrawer
 * This can be integrated into any page where you want to show the merge list
 */
const ToMergeListExample: React.FC = () => {
    const { t } = useTranslation();
    const [drawerOpen, setDrawerOpen] = useState(false);

    const handleOpenDrawer = () => {
        setDrawerOpen(true);
    };

    const handleCloseDrawer = () => {
        setDrawerOpen(false);
    };

    return (
        <Box sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>
                {t("Esempio Utilizzo Lista Pratiche da Unire")}
            </Typography>
            
            <Typography variant="body1" sx={{ mb: 3 }}>
                {t("Clicca il pulsante per aprire il drawer con la lista delle pratiche da unire.")}
            </Typography>

            <Button
                variant="contained"
                color="primary"
                onClick={handleOpenDrawer}
            >
                {t("Apri Lista Pratiche da Unire")}
            </Button>

            <ToMergeListDrawer
                open={drawerOpen}
                onClose={handleCloseDrawer}
            />
        </Box>
    );
};

export default ToMergeListExample;
