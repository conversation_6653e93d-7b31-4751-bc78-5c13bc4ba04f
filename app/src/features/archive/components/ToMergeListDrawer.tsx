import React from "react";
import { useTranslation } from "@1f/react-sdk";
import {
    Drawer,
    Box,
    Typography,
    IconButton,
    Divider,
} from "@vapor/react-material";
import { Close } from "@mui/icons-material";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import Spinner from "../../../custom-components/Spinner";
import ToMergeListFilters from "./ToMergeListFilters";
import { useToMergeListData } from "../hooks/useToMergeListData";

interface ToMergeListDrawerProps {
    open: boolean;
    onClose: () => void;
}

const ToMergeListDrawer: React.FC<ToMergeListDrawerProps> = ({
    open,
    onClose,
}) => {
    const { t } = useTranslation();
    const {
        query,
        setQuery,
        list,
        loading,
        resetFilters,
    } = useToMergeListData();

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleRowClick = (params: any) => {
        console.log("Row clicked:", params);
        // Add any row click functionality here
    };

    // Debug logging
    console.log("ToMergeListDrawer - Debug Info:", {
        loading,
        query,
        listRows: list.rows,
        listColumns: list.columns,
        totalRows: list.totalRows
    });

    return (
        <Drawer
            anchor="right"
            open={open}
            onClose={onClose}
            hideBackdrop
            sx={{
                "& .MuiDrawer-paper": {
                    width: "100%",
                    boxShadow:
                        "0px 0px 32px 8px rgba(0,0,0,0.22), 0px 4px 16px 0px rgba(0,0,0,0.18) !important",
                },
            }}
        >
            {/* Header */}
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    px: 3,
                    py: 2,
                }}
            >
                <Typography variant="h6" component="h2">
                    {t("Lista Pratiche da Unire")}
                </Typography>
                <IconButton onClick={onClose} color="primary">
                    <Close />
                </IconButton>
            </Box>
            <Divider />

            {/* Content */}
            <Box
                sx={{
                    backgroundColor: "#F2F6F8",
                    flexGrow: 1,
                    p: 3,
                    display: "flex",
                    flexDirection: "column",
                    gap: 2,
                }}
            >
                {/* Filters Section */}
                <ToMergeListFilters
                    query={query}
                    setQuery={setQuery}
                    onReset={resetFilters}
                />

                {/* Data Grid Section */}
                <Box
                    sx={{
                        backgroundColor: "white",
                        borderRadius: 1,
                        p: 2,
                        flexGrow: 1,
                        display: "flex",
                        flexDirection: "column",
                    }}
                >
                    {loading ? (
                        <Spinner fullPage={false} />
                    ) : (
                        <CustomDataGrid
                            name="toMergeList"
                            columns={list.columns}
                            data={list.rows}
                            page={list.page || 0}
                            totalRows={list.totalRows}
                            pageSize={list.pageSize}
                            loading={loading}
                            query={query}
                            onPageChangeCallback={onPageChangeCallback}
                            onClickCallback={handleRowClick}
                            onClickKey="uniqueid"
                        />
                    )}
                </Box>
            </Box>
        </Drawer>
    );
};

export default ToMergeListDrawer;
