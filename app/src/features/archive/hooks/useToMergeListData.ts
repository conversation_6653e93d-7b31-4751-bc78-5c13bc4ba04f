import { useCallback, useEffect, useState, useRef } from "react";
import { useTranslation } from "@1f/react-sdk";
import useGetCustom from "../../../hooks/useGetCustom";
import { getMergeListArchiveGrid } from "../../../utilities/mergeList/gridColumn";
import { IList } from "../../../interfaces/general.interfaces";

export interface IToMergeListQuery {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
    codeType: number;
    code: string;
    customer: string;
    counterpart: string;
}

const defaultQuery: IToMergeListQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "id",
    sortOrder: "desc",
    codeType: -1,
    code: "",
    customer: "",
    counterpart: "",
};

export const useToMergeListData = () => {
    const { t } = useTranslation();
    const filterRequest = useGetCustom(
        "archive/to-merge-list?noTemplateVars=true"
    );

    const [query, setQuery] = useState<IToMergeListQuery>(defaultQuery);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const filterToMergeList = useCallback(async () => {
        console.log("useToMergeListData - Manual API call with query:", query);

        try {
            const [columns, response]: any = await Promise.all([
                getMergeListArchiveGrid(t),
                filterRequest.doFetch(true, query),
            ]);

            console.log("useToMergeListData - API Response:", response);
            console.log("useToMergeListData - Columns:", columns);

            const { currentPage, totalRows } = response.data;

            const newList = {
                rows: currentPage || [],
                columns,
                totalRows: parseInt(totalRows) || 0,
                page: query.page,
                pageSize: query.pageSize,
                pageIndex: query.page,
            };

            console.log("useToMergeListData - Setting list:", newList);
            setList(newList);
        } catch (error) {
            console.error("Error fetching to-merge-list data:", error);
            setList({
                rows: [],
                columns: [],
                totalRows: 0,
                page: 0,
                pageSize: 10,
                pageIndex: 0,
            });
        }
    }, [t, query.page, query.pageSize, query.sortColumn, query.sortOrder, query.codeType, query.code, query.customer, query.counterpart]);

    // Auto-fetch data when query changes
    useEffect(() => {
        filterToMergeList();
    }, [filterToMergeList]);

    const resetFilters = useCallback(() => {
        setQuery({
            page: 0,
            pageSize: 10,
            sortColumn: "id",
            sortOrder: "desc",
            codeType: -1,
            code: "",
            customer: "",
            counterpart: "",
        });
    }, []);

    const updateQuery = useCallback((updates: Partial<IToMergeListQuery>) => {
        setQuery(prevQuery => ({
            ...prevQuery,
            ...updates,
            page: updates.page !== undefined ? updates.page : 0,
        }));
    }, []);

    return {
        query,
        setQuery: updateQuery,
        list,
        loading: filterRequest.loading,
        resetFilters,
        refetch: filterToMergeList,
    };
};
